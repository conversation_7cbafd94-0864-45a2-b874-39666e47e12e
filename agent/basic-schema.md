# 基本ResponseSchema
## <a name="successresponse"></a>SuccessResponse
```json
{ "success": true,}
```
## <a name="itemresponse"></a>ItemResponse
```json
{
    "success": true,
    "data": itemSchema
}
```
## <a name="listresponse"></a>ListResponse
```json
{
    "success": true,
    "total": 3,
    "data": itemSchema[]
}
```
## <a name="pagingresponse"></a>PagingResponse
```json
{
    "success": true,
    "total": 3,
    "skip": 0,
    "take": 3,
    "data": itemSchema[],
}
```
## <a name="errorresponse"></a>ErrorResponse
```json
{
    "success": true,
    "message": "發生錯誤"
}
```
## <a name="importresultresponse"></a>ImportResultResponse
```json
{
    "success": true,
    "data": itemSchema, // domain props type
    "errors": "發生錯誤"
}
```
