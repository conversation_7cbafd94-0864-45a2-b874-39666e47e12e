import { AssignmentType, EmployeeRole, OrganizationType } from "@/module/auth/auth.schema"
import { PermissionSubject as ps, PermissionAction as pa, PermissionViewAngles as pv, PermissionKey } from "./permissions.config"

export interface ParsedUserRoleArgs {
    orgId: string
    orgType: OrganizationType,
    role: EmployeeRole
    assignmentType: AssignmentType
}

export interface RoleMatcher<T> {
    view: string
    match: (parsed: ParsedUserRoleArgs) => boolean
    permissions: T[]
}

export const EmployeePermission: RoleMatcher<PermissionKey> = // ✅ 一般員工
{
    view: 'employeeView',
    match: ({ orgType }) =>
        orgType === OrganizationType.department || orgType === OrganizationType.office,
    permissions: [
        `${ps["work-item"]}:${pa.read}:${pv.self}`,
        `${ps["work-item"]}:${pa.create}:${pv.self}`,
        `${ps["work-item"]}:${pa.update}:${pv.self}`,
        `${ps["work-item"]}:${pa.update}:${pv.department}`,
        `${ps["work-item"]}:${pa.read}:${pv.department}`,

        `${ps["employee-report"]}:${pa.read}:${pv.self}`, //employee-report是虛的東西
        `${ps["employee-report"]}:${pa.submit}:${pv.self}`,// 不能幫別人送出
        `${ps["employee-report"]}:${pa.read}:${pv.department}`,

        `${ps["department-report"]}:${pa.read}:${pv.department}`,
        `${ps["department-report"]}:${pa.create}:${pv.department}`,
        `${ps["department-report"]}:${pa.update}:${pv.department}`,
        `${ps["department-report"]}:${pa.submit}:${pv.department}`,
    ],
}

export const CEOPermission: RoleMatcher<PermissionKey> = {
    view: 'ceoView',
    match: ({ orgId, orgType, role }) =>
        (orgId === 'o_company' && role === 'manager') ||
        (orgType === 'division' && role === 'manager'),
    permissions: [
        `${ps["work-item"]}:${pa.read}:${pv.all}`,
        `${ps["employee-report"]}:${pa.read}:${pv.all}`,
        `${ps["department-report"]}:${pa.read}:${pv.all}`,
        `${ps["company-statistics"]}:${pa.read}:${pv.all}`,
        `${ps["company-dashboard"]}:${pa.read}:${pv.all}`,
    ],
}

export const HrPermission: RoleMatcher<PermissionKey> =     // ✅ HR 組織內任何角色
{
    view: 'hrView',
    match: ({ orgId }) => orgId === 'o_department_hr',
    permissions: [
        ...(EmployeePermission.permissions as PermissionKey[]),
        ...(CEOPermission.permissions as PermissionKey[]),
    ],
}

export const rolePatternPermissionMap: RoleMatcher<PermissionKey>[] = [
    HrPermission,
    CEOPermission,
    EmployeePermission,
]
