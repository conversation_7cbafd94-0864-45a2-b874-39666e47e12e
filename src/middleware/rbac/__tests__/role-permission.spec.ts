import { ParsedUserRoleArgs, rolePatternPermissionMap } from "../role-permission"

const users = [
    { orgId: 'o_company', orgType: 'company', role: 'manager', assignmentType: 'primary' }, //Ricky
    { orgId: 'o_division_management', orgType: 'division', role: 'manager', assignmentType: 'primary' },//Arthur
    // <PERSON> & <PERSON>
    { orgId: 'o_department_information', orgType: 'department', role: 'manager', assignmentType: 'primary' },
    { orgId: 'o_department_information', orgType: 'department', role: 'member', assignmentType: 'primary' },
    // <PERSON> & Tom<PERSON>e
    { orgId: 'o_department_hr', orgType: 'department', role: 'manager', assignmentType: 'primary' },
    { orgId: 'o_department_hr', orgType: 'department', role: 'member', assignmentType: 'primary' },
    // office
    { orgId: 'o_office_audit', orgType: 'office', role: 'manager', assignmentType: 'primary' },
    { orgId: 'o_office_audit', orgType: 'office', role: 'member', assignmentType: 'primary' },
    // Emma
    { orgId: 'o_department_sustainability_management', orgType: 'department', role: 'manager', assignmentType: 'concurrent' },
    { orgId: 'o_department_quality', orgType: 'department', role: 'manager', assignmentType: 'primary' }
] as ParsedUserRoleArgs[]

describe('rolePatternPermissionMap.match()', () => {
    const getMatchedViews = (user: ParsedUserRoleArgs): string[] => {
        const result = rolePatternPermissionMap
            .filter(rule => rule.match(user))
            .map(rule => rule.view)
        console.log(user, result)
        return result
    }

    it('should match CEO 視角 for company and division managers', () => {
        expect(getMatchedViews(users[0])).toEqual(expect.arrayContaining(['ceoView']))
        expect(getMatchedViews(users[1])).toEqual(expect.arrayContaining(['ceoView']))
        expect(getMatchedViews(users[2])).toEqual(expect.arrayContaining(['employeeView']))
        expect(getMatchedViews(users[3])).toEqual(expect.arrayContaining(['employeeView']))
        expect(getMatchedViews(users[4])).toEqual(expect.arrayContaining(['hrView', 'employeeView']))
        expect(getMatchedViews(users[5])).toEqual(expect.arrayContaining(['hrView', 'employeeView']))
        expect(getMatchedViews(users[6])).toEqual(expect.arrayContaining(['employeeView']))
        expect(getMatchedViews(users[7])).toEqual(expect.arrayContaining(['employeeView']))
        expect(getMatchedViews(users[8])).toEqual(expect.arrayContaining(['employeeView']))
        expect(getMatchedViews(users[9])).toEqual(expect.arrayContaining(['employeeView']))
    })
})
