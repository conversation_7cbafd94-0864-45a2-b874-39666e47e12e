import { describeRoute } from 'hono-openapi'
import { resolver } from "hono-openapi/zod";
import z, { ZodSchema } from 'zod';
import { extendZodWithOpenApi } from 'zod-openapi';

extendZodWithOpenApi(z);// 重要

export function openApiMiddleware(tag: string, authKey: string[] = ['JwtKeyAuth']) {
    const tmp = authKey
    return function (
        respSchema: ZodSchema = z.object({}),
        description: string = '',
        authKey: string[] = tmp
    ) {
        return describeRoute({
            tags: [tag],
            description: description,
            security: authKey.map(key => ({ [key]: [] })),
            responses: {
                200: {
                    content: {
                        'application/json': { schema: resolver(respSchema) },
                    },
                },
            },
        })
    }
}
export function openApiTag(tag: string, defaultAuthKey: string[] = ['JwtKeyAuth']) {
    return function (
        props: {
            responsesSchema: ZodSchema,
            description: string,
            authKey?: string[],
            tags?: string[],
            summary?: string,
        }
    ) {
        const { responsesSchema, description, authKey, tags, summary } = props;
        return describeRoute({
            tags: [tag, ...(tags ? tags : [])],
            description: description || '',
            security: (authKey || defaultAuthKey).map(key => ({ [key]: [] })),
            summary: summary,
            responses: {
                200: {
                    content: {
                        'application/json': { schema: resolver(responsesSchema || z.object({})) },
                    },
                },
            },
        })
    }
}

export function fileApiMiddleware(description: string, respSchema: ZodSchema, req?: Object) {
    return describeRoute({
        tags: ['File'],
        description: description,
        security: { ['JwtKeyAuth']: [] },
        request: {
            ...req ? { request: { body: { ...req } } } : {}
        },
        responses: {
            200: {
                content: {
                    'application/json': { schema: resolver(respSchema) },
                },
            },
        },
    })
}