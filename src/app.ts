import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { errorHandler } from './middleware/errorHandler'
import { getPrisma } from './infra/db';
import { apiLog } from './middleware/apiLog';
import { z } from "zod";
import { cors } from 'hono/cors'
import { wrsModule } from './module/wrs/wrs.module';
import { env } from './env';
import { authModule } from './module/auth/auth.module';
import { bpmModule } from './module/bpm/bpm.module';
import { openApiTag } from './middleware/openapi';
import { employeeModule } from './module/hr/hr.module';

async function initInfra() {
    // 初始化資料庫連線
    getPrisma();
    console.log('Infra initialized')
}

function initRoute(app: Hono) {
    app.use('*', apiLog);
    app.use('*', cors())

    /**
     * health check
     */
    app.on('GET', ["/", "/swagger"],
        openApiTag("Health", [])({
            description: "Health Check",
            responsesSchema: z.object({ version: z.string() })
        }),
        async (c) => c.json({ version: env.VERSION })
    );
    // Router
    const authRouter = authModule()
    const commonAuthModule = { router: authRouter, path: '/v0/auth' };

    app.route('/v0/auth', authRouter);
    app.route('/v0', bpmModule([commonAuthModule]))// TODO(Mike): 與前端協調, 將path改為bpm/下
    app.route('/v0/wrs', wrsModule([commonAuthModule]))
    app.route('/v0/hr', employeeModule([commonAuthModule]))
}



async function main() {
    await initInfra();
    const app = new Hono();

    initRoute(app)

    // 掛載全域 custom error handler（應放在所有 router 之後）
    app.onError((err, c) => errorHandler(err, c))
    // 404 處理
    app.notFound((c) => {
        return c.json({ error: 'Not Found' }, 404)
    })

    // 啟動 server
    serve(
        { fetch: app.fetch, port: env.PORT, },
        () => {
            console.log(`Server running at ${env.HOST_URL}`)
            console.log(`Health check at ${env.HOST_URL}/swagger`)
        }
    )
}

main()
