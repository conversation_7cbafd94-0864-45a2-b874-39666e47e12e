import { jwtAuthMiddleware } from '@/middleware/auth'
import { Hono } from 'hono'
import { Context } from 'hono'
import { z } from 'zod'
import { EmployeeLifecycleService } from '../app/employeeLifecycle.service'
import { openApiTag } from '@/middleware/openapi'
import { ItemResponse, SuccessResponse } from '@/module/common/schema'
import { validator } from 'hono-openapi/zod'
import { EmployeeAssignmentPropsSchema, EmployeePropsSchema } from '../domain/schema/doamin.schema'
import { HrPermissionSubject } from '../rbac/permission.config'
import { checkPermission } from '@/middleware/checkPermission'


export function createEmployeeLifecycleRouter(service: EmployeeLifecycleService) {
  const router = new Hono()
  router.use('*', jwtAuthMiddleware)
  router.use('*', checkPermission(HrPermissionSubject['employee-lifecycle']))
  const openApiMiddleware = openApiTag('EmployeeLifecycle')

  /**
   * POST /hr/actions/onboard-employee：員工入職
   */
  router.post('/onboard-employee',
    openApiMiddleware({
      description: '員工入職',
      responsesSchema: ItemResponse(z.object({
        id: z.string().describe('任用紀錄 ID').openapi({ example: 'ea_FC0056_o_department_hr' }),
      })),
    }),
    validator('json', z.object({
      employee: EmployeePropsSchema.omit({ assignments: true }),
      assignment: EmployeeAssignmentPropsSchema.omit({ id: true, employeeId: true }),
    })),
    async (c: Context) => {
      const { employee, assignment } = await c.req.json()
      const result = await service.onboard({ employee, assignment })
      return c.json({ success: true, data: { id: result.assignment.props.id } })
    }
  )

  /**
   * POST /hr/actions/offboard-employee：員工離職
   */
  router.post('/offboard-employee',
    openApiMiddleware({
      description: '員工離職',
      responsesSchema: SuccessResponse,
    }),
    validator('json', z.object({
      employeeId: z.string().describe('員工 ID').openapi({ example: 'FC0011' }),
      endDate: z.string().datetime().describe('離職日').openapi({ example: '2025-12-31T00:00:00.000Z' }),
      changeReason: z.string().describe('異動原因').openapi({ example: 'resign' }),
    })),
    async (c: Context) => {
      const { employeeId, endDate, changeReason } = await c.req.json()
      await service.offboard({
        employeeId,
        endDate: new Date(endDate),
        changeReason,
      })
      return c.json({ success: true })
    }
  )


  router.post('/on-leave',
    openApiMiddleware({
      description: '員工請假 (設為 inactive 並終止所有任用)',
      responsesSchema: SuccessResponse
    }),
    validator('json', z.object({
      employeeId: z.string().describe('員工 ID').openapi({ example: 'FC0011' }),
      changeReason: z.string().describe('異動原因').openapi({ example: 'maternity leave' }),
    })),
    async (c) => {
      const { employeeId, changeReason } = c.req.valid('json')
      await service.onLeave({
        employeeId,
        endDate: new Date(), // 今天
        changeReason
      })
      return c.json({ success: true })
    }
  )

  return router
}
