import { Hono } from 'hono'
import { Context } from 'hono'

import { z } from 'zod'
import { EmployeeAssignmentService } from '../app/employeeAssignment.service'
import { jwtAuthMiddleware } from '@/middleware/auth'
import { openApiTag } from '@/middleware/openapi'
import { ItemResponse , SuccessResponse } from '@/module/common/schema'
import { EmployeeAssignmentPropsSchema } from '../domain/schema/doamin.schema'
import { validator } from 'hono-openapi/zod'
import { checkPermission } from '@/middleware/checkPermission'
import { HrPermissionSubject } from '../rbac/permission.config'


export function createEmployeeAssignmentRouter(service: EmployeeAssignmentService) {
  const router = new Hono()
  router.use('*', jwtAuthMiddleware)
  router.use('*',checkPermission(HrPermissionSubject['employee-assignments']))
  const openApiMiddleware = openApiTag('EmployeeAssignment')

  /**
   * POST /hr/employee-assignments：新增職責
   */
  router.post('/',
    openApiMiddleware({
      description: '新增職責',
      responsesSchema: ItemResponse(z.object({
        id: z.string().describe('任用紀錄 ID').openapi({ example: 'ea_FC0011_o_hr' }),
      })),
    }),
    validator('json', EmployeeAssignmentPropsSchema.omit({ id: true })),
    async (c: Context) => {
      const dto = await c.req.json()
      const result = await service.create(dto)
      return c.json({ success: true, data: { id: result.props.id } })
    }
  )

  /**
   * PUT /hr/employee-assignments/:id：更新職責
   */
  router.put('/:id',
    openApiMiddleware({
      description: '更新職責',
      responsesSchema: ItemResponse(EmployeeAssignmentPropsSchema),
    }),
    validator('json', EmployeeAssignmentPropsSchema),
    async (c: Context) => {
      const id = c.req.param('id')
      const dto  = await c.req.json()
      const result = await service.update({ ...dto, id })
      return c.json({ success: true, data: result.props })
    }
  )

  /**
   * PATCH /hr/employee-assignments/:id/terminate：終止職責
   */
  router.patch('/:id/terminate',
    openApiMiddleware({
      description: '終止職責',
      responsesSchema: SuccessResponse,
    }),
    validator('json', z.object({
      endDate: z.string().datetime().describe('結束日期').openapi({ example: '2025-12-31T00:00:00.000Z' }),
      changeReason: z.string().describe('異動原因').openapi({ example: 'retire' }),
    })),
    async (c: Context) => {
      const { endDate, changeReason }  = await c.req.json()
      const id = c.req.param('id')
      await service.terminate(id, new Date(endDate), changeReason)
      return c.json({ success: true })
    }
  )

  /**
   * DELETE /hr/employee-assignments/:id：刪除職責
   */
  router.delete('/:id',
    openApiMiddleware({
      description: '刪除職責(打錯的硬刪除)',
      responsesSchema: SuccessResponse,
    }),
    async (c: Context) => {
      const id = c.req.param('id')
      await service.delete(id)
      return c.json({ success: true })
    }
  )

  return router
}
