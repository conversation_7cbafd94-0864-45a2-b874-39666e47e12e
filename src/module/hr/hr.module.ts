import { getPrisma } from '@/infra/db'
import { CommonModule, createModuleSwagger } from '@/infra/openapiSpec'
import { Hono } from 'hono'
import { EmployeeRepository } from './infra/employee.repository'
import { EmployeeAssignmentRepository } from './infra/assignment.repository'
import { EmployeeLifecycleService } from './app/employeeLifecycle.service'
import { EmployeeAssignmentService } from './app/employeeAssignment.service'
import { swaggerUI } from '@hono/swagger-ui'
import { env } from '@/env'
import { createEmployeeLifecycleRouter } from './router/employeeLifecycle.router'
import { createEmployeeAssignmentRouter } from './router/employeeAssignment.router'

export function employeeModule(commonModules: CommonModule[]) {
    const prisma = getPrisma()

    // repository
    const employeeRepository = new EmployeeRepository(prisma)
    const employeeAssignmentRepository = new EmployeeAssignmentRepository(prisma)

    // services
    const lifecycleService = new EmployeeLifecycleService(employeeRepository, employeeAssignmentRepository)
    const assignmentService = new EmployeeAssignmentService(employeeAssignmentRepository)

    // router
    const actionRouter = createEmployeeLifecycleRouter(lifecycleService)
    const assignmentRouter = createEmployeeAssignmentRouter(assignmentService)

    const router = new Hono()
    const moduleName = 'hr'
    router.get('/', async (c) => c.json({ success: true }));

    router.route('/actions', actionRouter)
    router.route('/employee-assignments', assignmentRouter)

    // Swagger
    const employeeSpec = createModuleSwagger(
        router,
        {
            title: 'Employee HR API',
            description: '員工入離職與任用管理 API',
            modulePath: `/v0/${moduleName}`,
        },
        commonModules
    )
    router.get('/openapi-doc', employeeSpec)
    router.get('/swagger', swaggerUI({ url: `/v0/${moduleName}/openapi-doc` }))

    console.log(`* [Module] HR module initialized 
    - ${env.HOST_URL}/v0/${moduleName}
    - ${env.HOST_URL}/v0/${moduleName}/swagger`)

    return router
}
