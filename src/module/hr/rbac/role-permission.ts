import { ParsedUserRoleArgs } from "@/middleware/rbac/role-permission"
import { HrModulePermissionKey, HrPermissionSubject } from "./permission.config"

export interface RoleMatcher<T> {
    view: string
    match: (parsed: ParsedUserRoleArgs) => boolean
    permissions: T[]
}

export const HrPermission: RoleMatcher<HrModulePermissionKey> = {
    view: 'HrView',
    match: ({ orgId }) => orgId === 'o_department_hr',
    permissions: [
        HrPermissionSubject["employee-assignments"],
        HrPermissionSubject["employee-lifecycle"]
    ]
}

export const ItPermission: RoleMatcher<HrModulePermissionKey> = {
    view: 'ItView',
    match: ({ orgId }) => orgId === 'o_department_information',
    permissions: [
        HrPermissionSubject["employee-assignments"],
        HrPermissionSubject["employee-lifecycle"]
    ]
}
export const EmployeePermission: RoleMatcher<HrModulePermissionKey> = {
    view: 'EmployeeView',
    match: () => true,
    permissions: []
}

export const HrModuleRbacMap: RoleMatcher<HrModulePermissionKey>[] = [
    HrPermission,
    ItPermission,
    EmployeePermission,
]
