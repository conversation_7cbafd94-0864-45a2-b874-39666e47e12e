import { EmployeeLifecycleService } from "../employeeLifecycle.service"
import { createMockAssignmentRepository } from "./mocks/assignmentRepo.mock"
import { createMockEmployeeRepository } from "./mocks/employeeRepo.mock"


describe('EmployeeLifecycleService', () => {
  const prisma = { $transaction: jest.fn() } as any
  const employeeRepo = createMockEmployeeRepository()
  const assignmentRepo = createMockAssignmentRepository()
  const service = new EmployeeLifecycleService(employeeRepo, assignmentRepo)

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should onboard new employee with assignment', async () => {
    prisma.$transaction.mockImplementation(async (cb: any) => cb(prisma))

    await service.onboard({
      employee: {
        id: 'FC0011',
        email: '<EMAIL>',
        nickName: 'Rick',
        status: 'active',
        language: 'zh-TW',
        timezone: 'Asia/Taipei',
      },
      assignment: {
        orgId: 'o_hr',
        role: 'member',
        assignmentType: 'primary',
        startDate: '2025-06-17T00:00:00Z',
      },
    })

    expect(employeeRepo.create).toHaveBeenCalled()
    expect(assignmentRepo.create).toHaveBeenCalled()
  })

  it('should terminate active assignments on offboard', async () => {
    assignmentRepo.findActiveByEmployeeId.mockResolvedValue([
      {
        props: {
          id: 'a_FC0011_o_hr',
          orgId: 'o_hr',
          employeeId: 'FC0011',
          role: 'member',
          assignmentType: 'primary',
          startDate: '2024-01-01T00:00:00Z',
        },
        terminate: jest.fn(),
      } as any,
    ])
    prisma.$transaction.mockImplementation(async (cb: any) => cb(prisma))

    await service.offboard({
      employeeId: 'FC0011',
      endDate: new Date('2025-12-31'),
      changeReason: 'resign',
    })

    expect(assignmentRepo.update).toHaveBeenCalled()
  })
})
