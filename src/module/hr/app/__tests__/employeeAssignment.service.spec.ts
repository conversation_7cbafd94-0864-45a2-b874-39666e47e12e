import { EmployeeAssignmentService } from "../employeeAssignment.service"
import { createMockAssignmentRepository } from "./mocks/assignmentRepo.mock"


describe('EmployeeAssignmentService', () => {
  const assignmentRepo = createMockAssignmentRepository()
  const service = new EmployeeAssignmentService(assignmentRepo)

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should create assignment with generated ID', async () => {
    await service.create({
      employeeId: 'FC0011',
      orgId: 'o_hr',
      role: 'member',
      assignmentType: 'primary',
      startDate: '2025-06-01T00:00:00Z',
    })

    expect(assignmentRepo.create).toHaveBeenCalled()
    const createdAssignment = assignmentRepo.create.mock.calls[0][0]
    expect(createdAssignment.props.id).toBe('a_FC0011_o_hr')
  })

  it('should terminate assignment', async () => {
    assignmentRepo.findById.mockResolvedValue({
      props: {
        id: 'a_FC0011_o_hr',
        orgId: 'o_hr',
        employeeId: 'FC0011',
        role: 'member',
        assignmentType: 'primary',
        startDate: '2025-06-01T00:00:00Z',
      },
      terminate: jest.fn(),
    } as any)

    await service.terminate('a_FC0011_o_hr', new Date('2025-12-31'), 'resign')

    expect(assignmentRepo.update).toHaveBeenCalled()
  })

  it('should delete assignment by ID', async () => {
    await service.delete('a_FC0011_o_hr')
    expect(assignmentRepo.deleteById).toHaveBeenCalledWith('a_FC0011_o_hr')
  })
})
