import { EmployeeAssignmentRepository } from '@/module/hr/infra/assignment.repository'
import { PrismaClient } from '@prisma/client'

export function createMockAssignmentRepository(): jest.Mocked<EmployeeAssignmentRepository> {
  // 用空 PrismaClient 建構出 repository
  const repo = new EmployeeAssignmentRepository({} as PrismaClient)

  // 手動覆寫成 jest mock 函數
  repo.create = jest.fn()
  repo.update = jest.fn()
  repo.deleteById = jest.fn()
  repo.findById = jest.fn()
  repo.findManyByEmployeeId = jest.fn()
  repo.findActiveByEmployeeId = jest.fn()
  repo.deleteAllByEmployeeId = jest.fn()

  return repo as jest.Mocked<EmployeeAssignmentRepository>
}
