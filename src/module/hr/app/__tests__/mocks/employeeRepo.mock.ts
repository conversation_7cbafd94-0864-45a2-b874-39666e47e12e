import { EmployeeRepository } from '@/module/hr/infra/employee.repository'
import { PrismaClient } from '@prisma/client'

export function createMockEmployeeRepository(): jest.Mocked<EmployeeRepository> {
  const repo = new EmployeeRepository({} as PrismaClient)

  repo.create = jest.fn()
  repo.update = jest.fn()
  repo.delete = jest.fn()
  repo.findById = jest.fn()
  repo.findByEmail = jest.fn()

  return repo as jest.Mocked<EmployeeRepository>
}
