import { PrismaClient } from '@prisma/client'
import { EmployeeAssignmentRepository } from '../infra/assignment.repository'
import { EmployeeAssignment } from '../domain/employeeAssignment.entity'


export class EmployeeAssignmentService {
  constructor(
    private readonly assignmentRepo: EmployeeAssignmentRepository
  ) { }

  async create(dto: Omit<EmployeeAssignment['props'], 'id'>) {
    const id = EmployeeAssignment.generateId(dto.employeeId, dto.orgId)
    const assignment = new EmployeeAssignment({ ...dto, id })
    return this.assignmentRepo.create(assignment)
  }

  async update(dto: EmployeeAssignment['props']) {
    const assignment = new EmployeeAssignment(dto)
    return this.assignmentRepo.update(assignment)
  }

  async terminate(id: string, endDate: Date, reason: string) {
    const current = await this.assignmentRepo.findById(id)
    if (!current) throw new Error('Assignment not found')

    const terminated = new EmployeeAssignment({ ...current.props })
    terminated.terminate(endDate, reason)

    return this.assignmentRepo.update(terminated)
  }

  async delete(id: string) {
    await this.assignmentRepo.deleteById(id)
  }
}
