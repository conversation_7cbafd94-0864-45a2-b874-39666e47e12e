import { EmployeeRepository } from '../infra/employee.repository'
import { EmployeeAssignmentRepository } from '../infra/assignment.repository'
import { Employee } from '../domain/employee.aggregate'
import { EmployeeAssignment } from '../domain/employeeAssignment.entity'
import { getPrisma } from '@/infra/db'
import { EmployeeStatus } from '@/module/auth/auth.schema'


export class EmployeeLifecycleService {
  constructor(
    private readonly employeeRepo: EmployeeRepository,
    private readonly assignmentRepo: EmployeeAssignmentRepository
  ) { }

  async onboard(dto: {
    employee: Omit<Employee['props'], 'assignments'>,
    assignment: Omit<EmployeeAssignment['props'], 'id' | 'employeeId'>
  }) {
    const prisma = getPrisma()
    return await prisma.$transaction(async (tx) => {
      const employee = new Employee({ ...dto.employee, assignments: [] })
      await this.employeeRepo.create(employee, tx)

      const id = EmployeeAssignment.generateId(employee.props.id, dto.assignment.orgId)
      const assignment = new EmployeeAssignment({ ...dto.assignment, employeeId: employee.props.id, id })

      await this.assignmentRepo.create(assignment, tx)

      return { employee, assignment }
    })
  }

  async offboard(params: {
    employeeId: string
    endDate: Date
    changeReason: string
  }) {
    const { employeeId, endDate, changeReason } = params

    const prisma = getPrisma()
    await prisma.$transaction(async (tx) => {
      // 1. 找出所有 active assignment
      const assignments = await this.assignmentRepo.findActiveByEmployeeId(employeeId, tx)

      // 2. 終止所有任用
      for (const a of assignments) {
        a.terminate(endDate, changeReason)
        await this.assignmentRepo.update(a, tx)
      }

      // 3. 更新員工狀態為 left
      const employee = await this.employeeRepo.findById(employeeId, tx)
      if (!employee) throw new Error('Employee not found')

      employee.markLeft(changeReason) // ← domain 層方法
      await this.employeeRepo.update(employee, tx)
    })
  }

  async onLeave(params: {
    employeeId: string
    endDate: Date
    changeReason: string
  }) {
    const { employeeId, endDate, changeReason } = params
    const prisma = getPrisma()
    await prisma.$transaction(async (tx) => {
      // 找出 active 任用
      const assignments = await this.assignmentRepo.findActiveByEmployeeId(employeeId, tx)
      for (const a of assignments) {
        a.terminate(endDate, changeReason)
        await this.assignmentRepo.update(a, tx)
      }

      // 更新 employee status 為 inactive
      const employee = await this.employeeRepo.findById(employeeId, tx)
      if (!employee) throw new Error('Employee not found')

      employee.markInactive(changeReason)
      await this.employeeRepo.update(employee, tx)
    })
  }

}
