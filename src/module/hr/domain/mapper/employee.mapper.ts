import { Employee } from "../employee.aggregate"
import { EmployeeAssignmentMapper } from "./employeeAssignment.mapper"

export class EmployeeMapper {
  static fromPrisma(data: any): Employee {
    return new Employee({
      ...data,
      assignments: data.assignments?.map((a: any) => EmployeeAssignmentMapper.fromPrisma(a)) ?? [],
    })
  }

  static toPrisma(employee: Employee): any {
    const { assignments, ...rest } = employee.props
    return {
      ...rest,
      assignments: employee.assignments.map((a) => EmployeeAssignmentMapper.toPrisma(a)),
    }
  }
}