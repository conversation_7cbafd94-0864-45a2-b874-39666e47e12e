import { z } from 'zod'
import { extendZod<PERSON>ithOpenApi } from 'zod-openapi';
extendZodWithOpenApi(z);

export const EmployeeAssignmentPropsSchema = z.object({
    id: z.string().describe('任用紀錄 ID').openapi({ example: 'ea_123456' }),
    employeeId: z.string().describe('員工 ID').openapi({ example: 'FC0012' }),
    orgId: z.string().describe('所屬單位 ID').openapi({ example: 'o_department_hr' }),
    role: z.enum(['manager', 'member', 'intern', 'contractor']).describe('角色').openapi({ example: 'member' }),
    assignmentType: z.enum(['primary', 'concurrent', 'acting']).describe('任用類型').openapi({ example: 'primary' }),
    startDate: z.string().datetime().describe('任用開始日').openapi({ example: '2025-06-01T00:00:00Z' }),
    endDate: z.string().datetime().optional().describe('任用結束日').openapi({ example: '2025-12-31T00:00:00Z' }),
    changeReason: z.string().optional().describe('異動原因').openapi({ example: 'promotion' }),
})
export type EmployeeAssignmentProps = z.infer<typeof EmployeeAssignmentPropsSchema>

export const EmployeePropsSchema = z.object({
    id: z.string().describe('員工 ID').openapi({ example: 'FC0012' }),
    nickName: z.string().describe('暱稱').openapi({ example: '小明' }),
    email: z.string().email().describe('電子郵件').openapi({ example: '<EMAIL>' }),
    lastName: z.string().optional().describe('姓').openapi({ example: '王' }),
    firstName: z.string().optional().describe('名').openapi({ example: '小明' }),
    title: z.string().optional().describe('職稱').openapi({ example: '前端工程師' }),
    englishName: z.string().optional().describe('英文名').openapi({ example: 'Mike' }),
    imageUrl: z.string().url().optional().describe('大頭照 URL').openapi({ example: 'https://example.com/avatar.jpg' }),
    status: z.enum(['active', 'inactive', 'left']).describe('員工狀態').openapi({ example: 'active' }),
    language: z.string().describe('語言偏好（IETF BCP 47）').openapi({ example: 'zh-TW' }),
    timezone: z.string().describe('時區（IANA 格式）').openapi({ example: 'Asia/Taipei' }),
    assignments: z.array(EmployeeAssignmentPropsSchema).describe('任用紀錄列表').openapi({ example: [] }),
})

export type EmployeeProps = z.infer<typeof EmployeePropsSchema>


