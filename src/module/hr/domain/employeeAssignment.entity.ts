import { EmployeeAssignmentProps } from "./schema/doamin.schema"

export class EmployeeAssignment {
    constructor(public props: EmployeeAssignmentProps) { }

    static generateId(employeeId: string, orgId: string): string {
        return `a_${employeeId}_${orgId}`
    }

    terminate(endDate: Date, reason: string) {
        this.props.endDate = endDate.toISOString()
        this.props.changeReason = reason
    }
}