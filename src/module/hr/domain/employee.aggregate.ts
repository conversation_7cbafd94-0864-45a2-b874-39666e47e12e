import { EmployeeStatus } from "@/module/auth/auth.schema";
import { EmployeeAssignment } from "./employeeAssignment.entity";
import { EmployeeProps } from "./schema/doamin.schema";

export class Employee {
  public readonly assignments: EmployeeAssignment[]

  constructor(public readonly props: EmployeeProps) {
    this.assignments = props.assignments.map(a => new EmployeeAssignment(a))
  }
  onboardWith(assignment: EmployeeAssignment): { employee: Employee; assignment: EmployeeAssignment } {
    const hasConflict = this.props.assignments.some(
      (a) => a.orgId === assignment.props.orgId && !a.endDate
    )
    if (hasConflict) throw new Error('Cannot assign duplicate active assignment in same org')
    return { employee: this, assignment }
  }

  terminateAllAssignments(reason: string, endDate: Date) {
    this.assignments.forEach((a) => a.terminate(endDate, reason))
  }


  private setStatus(status: EmployeeStatus) {
    this.props.status = status
  }

  markInactive(reason: string) {
    this.setStatus(EmployeeStatus.inactive)
    // 可記錄異動原因，或在未來擴充為狀態紀錄表
  }

  markLeft(reason: string) {
    this.setStatus(EmployeeStatus.left)
    // 同上，可加上異動備註
  }

  resume() {
    this.setStatus(EmployeeStatus.active)
  }
}