import { Prisma, PrismaClient } from "@prisma/client"
import { EmployeeAssignmentMapper } from "../domain/mapper/employeeAssignment.mapper"
import { EmployeeAssignment } from "../domain/employeeAssignment.entity"


export class EmployeeAssignmentRepository {
  constructor(private readonly prisma: PrismaClient) {}

  async findById(id: string, tx?: Prisma.TransactionClient): Promise<EmployeeAssignment | null> {
    const db = tx ?? this.prisma
    const found = await db.employee_assignment.findUnique({ where: { id } })
    return found ? EmployeeAssignmentMapper.fromPrisma(found) : null
  }

  async findManyByEmployeeId(employeeId: string, tx?: Prisma.TransactionClient): Promise<EmployeeAssignment[]> {
    const db = tx ?? this.prisma
    const found = await db.employee_assignment.findMany({
      where: { employee_id: employeeId },
    })
    return found.map(EmployeeAssignmentMapper.fromPrisma)
  }

  async findActiveByEmployeeId(employeeId: string, tx?: Prisma.TransactionClient): Promise<EmployeeAssignment[]> {
    const db = tx ?? this.prisma
    const found = await db.employee_assignment.findMany({
      where: {
        employee_id: employeeId,
        end_date: null,
      },
    })
    return found.map(EmployeeAssignmentMapper.fromPrisma)
  }

  async create(assignment: EmployeeAssignment, tx?: Prisma.TransactionClient): Promise<EmployeeAssignment> {
    const db = tx ?? this.prisma
    const created = await db.employee_assignment.create({
      data: EmployeeAssignmentMapper.toPrisma(assignment),
    })
    return EmployeeAssignmentMapper.fromPrisma(created)
  }

  async update(assignment: EmployeeAssignment, tx?: Prisma.TransactionClient): Promise<EmployeeAssignment> {
    const db = tx ?? this.prisma
    const updated = await db.employee_assignment.update({
      where: { id: assignment.props.id },
      data: EmployeeAssignmentMapper.toPrisma(assignment),
    })
    return EmployeeAssignmentMapper.fromPrisma(updated)
  }

  async deleteById(id: string, tx?: Prisma.TransactionClient): Promise<void> {
    const db = tx ?? this.prisma
    await db.employee_assignment.delete({ where: { id } })
  }

  async deleteAllByEmployeeId(employeeId: string, tx?: Prisma.TransactionClient): Promise<void> {
    const db = tx ?? this.prisma
    await db.employee_assignment.deleteMany({ where: { employee_id: employeeId } })
  }
}
