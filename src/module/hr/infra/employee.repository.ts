import { PrismaClient, Prisma } from '@prisma/client'
import { Employee } from '../domain/employee.aggregate'
import { EmployeeMapper } from '../domain/mapper/employee.mapper'


export class EmployeeRepository {
  constructor(private readonly prisma: PrismaClient) {}

  async findById(id: string, tx?: Prisma.TransactionClient): Promise<Employee | null> {
    const db = tx ?? this.prisma
    const found = await db.employee.findUnique({
      where: { id },
      include: { assignments: true },
    })
    return found ? EmployeeMapper.fromPrisma(found) : null
  }

  async findByEmail(email: string, tx?: Prisma.TransactionClient): Promise<Employee | null> {
    const db = tx ?? this.prisma
    const found = await db.employee.findUnique({
      where: { email },
      include: { assignments: true },
    })
    return found ? EmployeeMapper.fromPrisma(found) : null
  }

  async create(employee: Employee, tx?: Prisma.TransactionClient): Promise<Employee> {
    const db = tx ?? this.prisma
    const created = await db.employee.create({
      data: EmployeeMapper.toPrisma(employee),
    })
    return EmployeeMapper.fromPrisma({ ...created, assignments: [] }) // 預設 assignments 為空
  }

  async update(employee: Employee, tx?: Prisma.TransactionClient): Promise<Employee> {
    const db = tx ?? this.prisma
    const updated = await db.employee.update({
      where: { id: employee.props.id },
      data: EmployeeMapper.toPrisma(employee),
    })
    return EmployeeMapper.fromPrisma({ ...updated, assignments: employee.assignments.map(a => a.props) })

  }

  async delete(id: string, tx?: Prisma.TransactionClient): Promise<void> {
    const db = tx ?? this.prisma
    await db.employee.delete({ where: { id } })
  }
}
