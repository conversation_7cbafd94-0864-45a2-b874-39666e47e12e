import { Context, Hono } from 'hono'
import { jwtAuthMiddleware } from '@/middleware/auth'
import { openApiTag } from '@/middleware/openapi'
import { z } from '@hono/zod-openapi';
import { JwtPayload } from '@/module/auth/auth.schema';
import { PermissionError } from '@/errors/app.error';
import { ParsedUserRoleArgs, rolePatternPermissionMap } from '@/middleware/rbac/role-permission';
import { successBase } from '@/module/common/schema';

export function createWrsViewRouter() {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const openApiMiddleware = openApiTag("WrsView");

    /**
     * TODO:
     * GET /v0/wrs/view/my
    */
    router.get('/my',
        openApiMiddleware({
            description: "查看我有哪些視角可以使用",
            summary:'[side_bar]',
            responsesSchema: successBase(
                z.object({
                    selfView: z.boolean(),
                    departmentView: z.boolean(),
                    hrView: z.boolean(),
                    ceoView: z.boolean()
                }))
        }),
        async (c: Context) => {
            const user = c.get('user') as JwtPayload;
            if (!user.assignments) throw new PermissionError('PermissionError', 'Forbidden: User role not found in context')

            const getMatchedViews = (user: ParsedUserRoleArgs): string[] => {
                const result = rolePatternPermissionMap
                    .filter(rule => rule.match(user))
                    .map(rule => rule.view)
                return result
            }

            let views: string[] = []
            for (let assignment of user.assignments) {
                const a = getMatchedViews(assignment)
                views = [...views, ...a]
            }

            return c.json({
                success: true,
                data: {
                    selfView: views.indexOf('employeeView') !== -1,
                    deparementView: views.indexOf('employeeView') !== -1,
                    hrView: views.indexOf('hrView') !== -1,
                    ceoView: views.indexOf('ceoView') !== -1
                }
            });
        }
    );

    return router
}
