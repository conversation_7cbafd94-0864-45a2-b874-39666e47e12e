import { PaginationListQuery, ListQuery } from "@/module/common/query.schema";
import { WrsDepartmentReportPropsSchema, WrsEmployeeReportPropsSchema, WrsWeekPropsSchema, WrsWorkItemPropsSchema } from "./domain.schema";
import { z } from "zod";

export const DepartmentReportListQuerySchema=ListQuery(WrsDepartmentReportPropsSchema)
export type DepartmentReportListQuery= z.infer<typeof DepartmentReportListQuerySchema>;

export const EmployeeReportPaginationQuerySchema=PaginationListQuery(WrsEmployeeReportPropsSchema)
export type EmployeeReportPaginationQuery= z.infer<typeof EmployeeReportPaginationQuerySchema>;

export const WorkItemListQuerySchema=ListQuery(WrsWorkItemPropsSchema.pick({
  orgId: true,
  weekId: true,
  type: true,
}))
export type WorkItemListQuery= z.infer<typeof WorkItemListQuerySchema>;

export const MyWorkItemListQuerySchema=ListQuery(WrsWorkItemPropsSchema.pick({
  weekId: true,
}))
export type MyWorkItemListQuery= z.infer<typeof MyWorkItemListQuerySchema>;

export const MyOrgWorkItemListQuerySchema=ListQuery(WrsWorkItemPropsSchema.pick({
  employeeId: true,
  weekId: true,
}))
export type MyOrgWorkItemListQuery= z.infer<typeof MyOrgWorkItemListQuerySchema>;

export const WeekPaginationQuerySchema=PaginationListQuery(WrsWeekPropsSchema)
export type WeekPaginationQuery= z.infer<typeof WeekPaginationQuerySchema>;

export const ListEmpReportQuerySchema=WrsEmployeeReportPropsSchema.pick({
  id: true,
  employeeId: true,
  orgId: true,
  weekId: true,
  status: true,
}).partial().describe('員工週報查詢參數，包含id、employeeId、orgId、weekId、status等欄位')
export type ListEmpReportQuery= z.infer<typeof ListEmpReportQuerySchema>;
export const ListDepartmentReportQuerySchema=WrsDepartmentReportPropsSchema.pick({
  id: true,
  weekId: true,
  status: true,     
}).partial().describe('部門週報查詢參數，包含id、orgId、weekId、status等欄位')
export type ListDepartmentReportQuery= z.infer<typeof ListDepartmentReportQuerySchema>;