import { WrsEmployeeReportRepository } from '../infra/WrsEmployeeReport.repository'
import { WrsEmployeeReport } from '../domain/WrsEmployeeReport'
import { CreateWrsWorkItemProps, WrsEmployeeReportProps, WrsWorkItemProps } from '../domain/domain.schema'
import { EmployeeReportPaginationQuery, EmployeeReportPaginationQuerySchema, ListEmpReportQuery, ListEmpReportQuerySchema } from '../domain/query.schema'
import { EmployeeAggregation, StatisticsEmployeeReportResponse } from '../domain/response.schema';
import { nanoid } from 'nanoid';
import { WrsWeekRepository } from '../infra/WrsWeek.repository';
import { WrsWorkItemRepository } from '../infra/WrsWorkItem.repository';
import { JwtPayload } from '@/module/auth/auth.schema';
import { NotFoundError } from '@/errors/app.error';

export class WrsEmployeeReportService {

    constructor(
        private readonly repo: WrsEmployeeReportRepository,
        private readonly wrsWeekRepo: WrsWeekRepository,
        private readonly wrsWorkItemRepo: WrsWorkItemRepository
    ) { }
    /**
     * 列出「所有人」週報統計(可透過query指定部門或員工)
     */
    async statistics(user: JwtPayload, params?: ListEmpReportQuery): Promise<StatisticsEmployeeReportResponse[]> {
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const safeParams: ListEmpReportQuery = ListEmpReportQuerySchema.partial().parse(params);
        safeParams.orgId = orgId;
        const result = await this.repo.statistics(safeParams);
        return result
    }
    /**
     * 查詢所有個人週報列表，支援分頁與條件
     */
    async paging(params?: EmployeeReportPaginationQuery): Promise<{ data: EmployeeAggregation[], total: number }> {
        const safeParams: EmployeeReportPaginationQuery = EmployeeReportPaginationQuerySchema.parse(params || {});
        return this.repo.pagingList(safeParams)
    }

    /**
     * 建立個人週報草稿
     */
    async create(reportProps: WrsEmployeeReportProps): Promise<WrsEmployeeReport> {
        return this.repo.create(reportProps)
    }

    async getLast(userId: string): Promise<EmployeeAggregation> {
        const draft = await this.repo.findOne({ employeeId: userId, status: 'draft' })
        if (draft) {
            return draft
        }
        const lastReport = await this.repo.findOne({ employeeId: userId, status: 'submitted' })

        if (lastReport) {
            return lastReport
        }
        throw new NotFoundError('NO_LAST_REPORT', 'No last report found for user')
    }
    /**
     * 查詢單一個人週報
     */
    async getById(id: string): Promise<EmployeeAggregation> {
        return this.repo.findById(id)
    }

    /**
     * 更新個人週報草稿
     */
    async updateDraft(id: string, updateProps: Partial<WrsEmployeeReportProps>): Promise<WrsEmployeeReport> {
        return this.repo.updateDraft(id, updateProps)
    }

    /**
     * 提交個人週報
     */
    async submit(id: string): Promise<WrsEmployeeReport> {
        return this.repo.submit(id)
    }
}
